Congratulations! You have successfully deployed and connected to a secure remote MCP server.

### Continue to the next lab

This lab is the first lab in a three-part series. In the second lab, you will use the MCP server you created with an ADK Agent.

[Use an MCP Server on Cloud Run with an ADK Agent](https://codelabs.developers.google.com/codelabs/cloud-run-adk-agent)

### (Optional) Clean up

If you are not continuing on to the next lab and you would like to clean up what you have created, you can delete your Cloud project to avoid incurring additional charges.

While Cloud Run does not charge when the service is not in use, you might still be charged for storing the container image in Artifact Registry. Deleting your Cloud project stops billing for all the resources used within that project.

If you would like, delete the project:

```bash
gcloud projects delete $GOOGLE_CLOUD_PROJECT
```

You may also want to delete unnecessary resources from your cloudshell disk. You can:

Delete the codelab project directory:

```bash
rm -rf ~/mcp-on-cloudrun
```

**Warning!** This next action is can't be undone! If you would like to delete everything on your Cloud Shell to free up space, you can delete your whole`home` directory. Be careful that everything you want to keep is saved somewhere else.

```bash
sudo rm -rf $HOME
```
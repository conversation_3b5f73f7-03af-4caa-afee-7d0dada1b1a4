To verify that your Cloud Run MCP server was called, check the service logs.

```bash
gcloud run services logs read zoo-mcp-server --region europe-west1 --limit=5
```

You should see an output log that confirms a tool call was made. 🛠️

```
2025-08-05 19:50:31 INFO: 169.254.169.126:39444 - "POST /mcp/ HTTP/1.1" 200 OK
2025-08-05 19:50:31 [INFO]: Processing request of type CallToolRequest
2025-08-05 19:50:31 [INFO]: >>> 🛠️ Tool: 'get_animals_by_species' called for 'penguin'
```

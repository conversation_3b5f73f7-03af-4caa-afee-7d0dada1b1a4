Click this link to navigate directly to Cloud Shell Editor If prompted to authorize at any point today, click Authorize to continue. Click to authorize Cloud Shell If the terminal doesn't appear at the bottom of the screen, open it:

- Click View
- Click Terminal
- Open new terminal in Cloud Shell Editor

In the terminal, set your project with this command:

Format:
```bash
gcloud config set project [PROJECT_ID]
```

Example:
```bash
gcloud config set project lab-project-id-example
```

If you can't remember your project id:

You can list all your project ids with:
```bash
gcloud projects list | awk '/PROJECT_ID/{print $2}'
```

Set project id in Cloud Shell Editor terminal

You should see this message:

```
Updated property [core/project].
```

If you see a WARNING and are asked `Do you want to continue (Y/n)?`, then you have likely entered the project ID incorrectly. Press `n`, press `Enter`, and try to run the `gcloud config set project` command again.
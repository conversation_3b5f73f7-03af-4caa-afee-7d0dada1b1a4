In this step, you'll create the job with direct VPC egress for faster uploads to Google Cloud Storage.

Create the Cloud Run Job:
```bash
gcloud beta run jobs create $JOB_NAME \
  --region $REGION \
  --image $REGION-docker.pkg.dev/$PROJECT_ID/$AR_REPO/$IMAGE_NAME \
  --set-env-vars BUCKET_NAME=$BUCKET_NAME \
  --set-secrets HF_TOKEN=$SECRET_ID:latest \
  --cpu 8.0 \
  --memory 32Gi \
  --gpu 1 \
  --add-volume name=finetuned_model,type=cloud-storage,bucket=$BUCKET_NAME \
  --add-volume-mount volume=finetuned_model,mount-path=/finetune/new_model \
  --service-account $SERVICE_ACCOUNT_ADDRESS
```

Execute the job:
```bash
gcloud beta run jobs execute $JOB_NAME --region $REGION --async
```
The job will take around 10 minutes to complete. You can check on the status using the link provided in the output of the last command.

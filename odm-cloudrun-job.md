# OpenDroneMap Cloud Run Job

In this codelab, you will use Cloud Run jobs to process aerial imagery using OpenDroneMap (ODM), an open-source toolkit for processing drone images into maps and 3D models.

### What you'll do
- Process aerial imagery using the existing OpenDroneMap Docker image
- Generate orthorectified imagery, digital surface models, and 3D models from drone photos
- Use Cloud Storage to manage input images and output products

### What you'll learn
- How to run OpenDroneMap using Cloud Run Jobs
- How to use Cloud Storage for input/output data management
- How to process aerial imagery at scale in the cloud

---

# Before you begin

### Enable APIs
Before you can start using this codelab, enable the following APIs by running:
```bash
gcloud services enable run.googleapis.com \
  compute.googleapis.com \
  cloudbuild.googleapis.com \
  artifactregistry.googleapis.com
```

### OpenDroneMap Overview
OpenDroneMap (ODM) is an open-source toolkit that processes aerial imagery from drones or other platforms to create:
- Orthorectified imagery (georeferenced maps)
- Digital Surface Models (DSM)
- Digital Terrain Models (DTM)
- Textured 3D models
- Classified point clouds

The standard Docker command for ODM is:
```bash
docker run -ti --rm -v /my/project:/datasets/code opendronemap/odm --project-path /datasets
```

---

# Setup and Requirements

Set up the following resources:
- IAM service account and associated IAM permissions
- Cloud Storage bucket to store input images and output products

Set environment variables for this codelab:
```bash
export PROJECT_ID=<YOUR_PROJECT_ID>
export REGION=<YOUR_REGION>
export JOB_NAME=odm-processing-job
export BUCKET_NAME=$PROJECT_ID-odm-processing
export SERVICE_ACCOUNT="odm-job-sa"
export SERVICE_ACCOUNT_ADDRESS=$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com
```

Create the service account:
```bash
gcloud iam service-accounts create $SERVICE_ACCOUNT \
  --display-name="Service account for ODM processing"
```

Create a bucket to host your input images and output products:
```bash
gcloud storage buckets create -l $REGION gs://$BUCKET_NAME
```

Grant your service account access to the bucket:
```bash
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME \
  --member=serviceAccount:$SERVICE_ACCOUNT_ADDRESS \
  --role=roles/storage.objectAdmin
```

---

# Prepare Your Input Data

OpenDroneMap requires aerial images to be organized in a specific structure. Create the following folder structure in your Cloud Storage bucket:

```
gs://your-bucket/
├── images/           # Place your JPEG/TIFF drone images here
├── gcp_list.txt     # Optional: Ground Control Points file
└── outputs/         # ODM will write results here
```

Upload your drone images to the images folder:
```bash
# Example: Upload images from local directory
gcloud storage cp /path/to/your/drone/images/* gs://$BUCKET_NAME/images/
```

### Supported Input Formats
- JPEG images from drones/cameras
- TIFF images (8-bit and 16-bit)
- Ground Control Points (optional, for georeferencing)

---

# Deploy and Execute the ODM Job

Create the Cloud Run Job using the existing OpenDroneMap Docker image:
```bash
gcloud run jobs create $JOB_NAME \
  --region $REGION \
  --image opendronemap/odm \
  --cpu 4.0 \
  --memory 16Gi \
  --task-timeout 3600 \
  --max-retries 1 \
  --add-volume name=project_data,type=cloud-storage,bucket=$BUCKET_NAME \
  --add-volume-mount volume=project_data,mount-path=/datasets \
  --service-account $SERVICE_ACCOUNT_ADDRESS \
  --args="--project-path,/datasets"
```

### Optional: Add ODM Processing Parameters
You can customize the ODM processing by adding specific parameters:
```bash
gcloud run jobs create $JOB_NAME \
  --region $REGION \
  --image opendronemap/odm \
  --cpu 4.0 \
  --memory 16Gi \
  --task-timeout 3600 \
  --max-retries 1 \
  --add-volume name=project_data,type=cloud-storage,bucket=$BUCKET_NAME \
  --add-volume-mount volume=project_data,mount-path=/datasets \
  --service-account $SERVICE_ACCOUNT_ADDRESS \
  --args="--project-path,/datasets,--dsm,--dtm,--orthophoto-resolution,2"
```

Common ODM parameters:
- `--dsm`: Generate Digital Surface Model
- `--dtm`: Generate Digital Terrain Model  
- `--orthophoto-resolution X`: Set orthophoto resolution in cm/pixel
- `--mesh-size X`: Set maximum number of vertices in mesh
- `--feature-quality high`: Use high quality feature detection

Execute the job:
```bash
gcloud run jobs execute $JOB_NAME --region $REGION --async
```

The job processing time depends on:
- Number and resolution of input images
- Selected processing options
- Available CPU/memory resources

You can monitor the job status in the Cloud Console or using:
```bash
gcloud run jobs executions list --job=$JOB_NAME --region=$REGION
```

---

# Retrieve Your Results

After the job completes successfully, your processed outputs will be available in the Cloud Storage bucket:

```
gs://your-bucket/
├── images/                    # Your original input images
├── odm_orthophoto/           # Orthorectified imagery
├── odm_dem/                  # Digital Elevation Models
├── odm_meshing/              # 3D mesh models
├── odm_texturing/            # Textured 3D models
├── odm_georeferencing/       # Georeferencing data
└── odm_report/               # Processing reports and logs
```

Download the results:
```bash
# Download all outputs
gcloud storage cp -r gs://$BUCKET_NAME/odm_* ./odm_outputs/

# Or download specific products
gcloud storage cp gs://$BUCKET_NAME/odm_orthophoto/odm_orthophoto.tif ./orthophoto.tif
gcloud storage cp gs://$BUCKET_NAME/odm_dem/dsm.tif ./surface_model.tif
```

### Output Products
- **Orthophoto**: `odm_orthophoto/odm_orthophoto.tif` - Georeferenced aerial map
- **Digital Surface Model**: `odm_dem/dsm.tif` - Elevation data including objects
- **Digital Terrain Model**: `odm_dem/dtm.tif` - Ground elevation data
- **3D Model**: `odm_texturing/odm_textured_model.obj` - Textured 3D mesh
- **Point Cloud**: `odm_georeferencing/odm_georeferenced_model.laz` - Classified point cloud

---

# Scaling and Optimization

### For Large Datasets
If processing large datasets (100+ images), consider:

1. **Increase resources**:
```bash
--cpu 8.0 --memory 32Gi --task-timeout 7200
```

2. **Split processing** into smaller chunks by organizing images into separate folders

3. **Use split-merge workflow** for very large datasets:
```bash
--args="--project-path,/datasets,--split,400,--split-overlap,150"
```

### Cost Optimization
- Use preemptible instances for non-urgent processing
- Monitor processing logs to optimize resource allocation
- Consider regional bucket placement near compute resources

---

# Troubleshooting

### Common Issues
1. **Insufficient memory**: Increase `--memory` parameter
2. **Timeout errors**: Increase `--task-timeout` value
3. **Poor georeferencing**: Add Ground Control Points (GCP) file
4. **Low quality outputs**: Increase `--orthophoto-resolution` or use `--feature-quality high`

### Monitoring
Check job logs:
```bash
gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME" --limit 50
```

---

# Congratulations!

You have successfully processed aerial imagery using OpenDroneMap on Cloud Run Jobs!

### What we've covered
- How to run OpenDroneMap using Cloud Run Jobs
- How to manage input images and output products with Cloud Storage
- How to scale aerial imagery processing in the cloud
- How to optimize processing parameters for different use cases

### Next Steps
- Explore advanced ODM parameters for specialized processing
- Integrate with other GIS tools and workflows
- Set up automated processing pipelines
- Consider WebODM for a user-friendly interface

---

# Clean up

To avoid charges, delete the resources you created:

Delete the Cloud Run job:
```bash
gcloud run jobs delete $JOB_NAME --region $REGION
```

Delete the Cloud Storage bucket:
```bash
gcloud storage rm -r gs://$BUCKET_NAME
```

Delete the service account:
```bash
gcloud iam service-accounts delete $SERVICE_ACCOUNT_ADDRESS
```

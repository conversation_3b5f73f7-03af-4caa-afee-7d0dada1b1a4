An MCP prompt can speed up your workflow for prompts you run often by creating a shorthand for a longer prompt.

Gemini CLI automatically converts MCP prompts into custom slash commands so that you can invoke an MCP prompt by typing `/prompt_name` where `prompt_name` is the name of your MCP prompt.

Create an MCP prompt so you can quickly find an animal in the zoo by typing `/find animal` into Gemini CLI.

Add this code to your `server.py` file above the main guard (`if __name__ == "__main__":`)

```python
@mcp.prompt()
def find(animal: str) -> str:
    """
    Find which exhibit and trail a specific animal might be located.
    """
    return (
        f"Please find the exhibit and trail information for {animal} in the zoo. "
        f"Respond with '[animal] can be found in the [exhibit] on the [trail].'"
        f"Example: Penguins can be found in The Arctic Exhibit on the Polar Path."
    )
```

Re-deploy your application to Cloud Run

```bash
gcloud run deploy zoo-mcp-server \
  --no-allow-unauthenticated \
  --region=europe-west1 \
  --source=. \
  --labels=dev-tutorial=codelab-mcp
```

Refresh your `ID_TOKEN` for your remote MCP server

```bash
export ID_TOKEN=$(gcloud auth print-identity-token)
```

After the new version of your application is deployed, start Gemini CLI.

```bash
gemini
```

In the prompt use the new custom command that you created:

```
/find --animal="lions"
```

You should see that Gemini CLI calls the `get_animals_by_species` tool and formats the response as instructed by the MCP prompt!

```
╭───────────────────────────╮
│ > /find --animal="lion"   │
╰───────────────────────────╯
╭───────────────────────────────────────────────────────────────────────────────────────────────────╮
│ ✔ get_animals_by_species (zoo-remote MCP Server) get_animals_by_species (zoo-remote MCP Server) │
│                                                                                                   │
│ [{"species":"lion","name":"Leo","age":7,"enclosure":"The Big Cat                                  │
│ Plains","trail":"Savannah                                                                         │
│ Heights"},{"species":"lion","name":"Nala","age":6,"enclosure":"The Big Cat                         │
│ Plains","trail":"Savannah                                                                         │
│ Heights"},{"species":"lion","name":"Simba","age":3,"enclosure":"The Big Cat                        │
│ Plains","trail":"Savannah                                                                         │
│ Heights"},{"species":"lion","name":"King","age":8,"enclosure":"The Big Cat                         │
│ Plains","trail":"Savannah Heights"}]                                                               │
╰───────────────────────────────────────────────────────────────────────────────────────────────────╯
✦ Lions can be found in The Big Cat Plains on the Savannah Heights.
```

### Stretch Goals to Test Yourself

For an extra challenge, see if you can follow the same steps to create a prompt for returning fun facts about specific animal species in the zoo.

Or as an even larger stretch to test what you have learned, come up with an idea for a tool you would use frequently and deploy a second remote MCP server. Then add it to your Gemini CLI settings to see if it works.

### Debugging

If you see an error like this:

```
✕ Unknown command: /find --animal="lions"
```

Try to run `/mcp` and if it outputs `zoo-remote - Disconnected`, you might have to re-deploy, or run the following commands again:

```shell
gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
  --member=user:$(gcloud config get-value account) \
  --role='roles/run.invoker'

export PROJECT_NUMBER=$(gcloud projects describe $GOOGLE_CLOUD_PROJECT --format="value(projectNumber)")

export ID_TOKEN=$(gcloud auth print-identity-token)
```
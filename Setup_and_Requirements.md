Set up the following resources:- IAM service account and associated IAM permissions,- Secret Manager secret to store your Hugging Face token,- Cloud Storage bucket to store your fine-tuned model, and- Artifact Registry repository to store the image you'll build to fine-tune your model.Set environment variables for this codelab. We pre-populated a number of variables for you. Specify your project ID, region, and Hugging Face token.```bash
export PROJECT_ID=<YOUR_PROJECT_ID>
export REGION=<YOUR_REGION>
export HF_TOKEN=<YOUR_HF_TOKEN>
export NEW_MODEL=gemma-emoji
export AR_REPO=codelab-finetuning-jobs
export IMAGE_NAME=finetune-to-gcs
export JOB_NAME=finetuning-to-gcs-job
export BUCKET_NAME=$PROJECT_ID-codelab-finetuning-jobs
export SECRET_ID=HF_TOKEN
export SERVICE_ACCOUNT="finetune-job-sa"
export SERVICE_ACCOUNT_ADDRESS=$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com
```Create the service account by running this command:```bash
gcloud iam service-accounts create $SERVICE_ACCOUNT \
  --display-name="Service account for fine-tuning codelab"
```Use Secret Manager to store Hugging Face access token:```bash
gcloud secrets create $SECRET_ID \
  --replication-policy="automatic"
printf $HF_TOKEN | gcloud secrets versions add $SECRET_ID --data-file=-
```Grant your service account the role of Secret Manager Secret Accessor:```bash
gcloud secrets add-iam-policy-binding $SECRET_ID \
  --member serviceAccount:$SERVICE_ACCOUNT_ADDRESS \
  --role='roles/secretmanager.secretAccessor'
```Create a bucket that will host your fine-tuned model:```bash
gcloud storage buckets create -l $REGION gs://$BUCKET_NAME
```Grant your service account access to the bucket:```bash
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME \
  --member=serviceAccount:$SERVICE_ACCOUNT_ADDRESS \
  --role=roles/storage.objectAdmin
```Create an Artifact Registry repository to store the container image:```bash
gcloud artifacts repositories create $AR_REPO \
  --repository-format=docker \
  --location=$REGION \
  --description="codelab for finetuning using CR jobs" \
  --project=$PROJECT_ID
```
Now that you've successfully deployed a remote MCP server, you can connect to it using various applications like Google Code Assist or Gemini CLI. In this section, we will establish a connection to your new remote MCP server using Gemini CLI.

**Note:** Gemini CLI comes pre-installed in Cloud Shell.

Give your user account permission to call the remote MCP server

```bash
gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
  --member=user:$(gcloud config get-value account) \
  --role='roles.run.invoker'
```

Save your Google Cloud credentials and project number in environment variables for use in the Gemini Settings file:

```bash
export PROJECT_NUMBER=$(gcloud projects describe $GOOGLE_CLOUD_PROJECT --format="value(projectNumber)")
export ID_TOKEN=$(gcloud auth print-identity-token)
```

Open your Gemini CLI Settings file

```bash
cloudshell edit ~/.gemini/settings.json
```

Replace your Gemini CLI settings file to add the Cloud Run MCP server

```json
{
  "mcpServers": {
    "zoo-remote": {
      "httpUrl": "https://zoo-mcp-server-$PROJECT_NUMBER.europe-west1.run.app/mcp/",
      "headers": {
        "Authorization": "Bearer $ID_TOKEN"
      }
    }
  },
  "selectedAuthType": "cloud-shell",
  "hasSeenIdeIntegrationNudge": true
}
```

Start the Gemini CLI in Cloud Shell

```bash
gemini
```

You may need to press `Enter` to accept some default settings. Gemini CLI initial view Have gemini list the MCP tools available to it within its context

```
/mcp
```

Ask gemini to find something in the zoo

```
Where can I find penguins?
```

The Gemini CLI should know to use the `zoo-remote` MCP Server and will ask if you would like to allow execution of MCP. Use the down arrow, then press `Enter` to select `Yes, always allow all tools from server "zoo-remote"`

![Gemini CLI allow zoo remote tools](https://codelabs.developers.google.com/codelabs/cloud-run/img/6a1f3b0b0b0b0b0b.png)

The output should show the correct answer and a display box showing that the MCP server was used.

![Gemini CLI show zoo mcp server result](https://codelabs.developers.google.com/codelabs/cloud-run/img/7a1f3b0b0b0b0b0b.png)

You have done it! You have successfully deployed a remote MCP server to Cloud Run and tested it using Gemini CLI.

When you are ready to end your session, type `/quit` and then press `Enter` to exit Gemini CLI.

### Debugging

If you see an error like this:

```
🔍 Attempting OAuth discovery for 'zoo-remote'...
❌ 'zoo-remote' requires authentication but no OAuth configuration found
Error connecting to MCP server 'zoo-remote': MCP server 'zoo-remote' requires authentication. Please configure OAuth or check server settings.
```

It is likely that the ID Token has timed out and requires setting the `ID_TOKEN` again.

Type `/quit` and then press `Enter` to exit Gemini CLI. Set your project in your terminal

```bash
gcloud config set project [PROJECT_ID]
```

Restart on step 2 above
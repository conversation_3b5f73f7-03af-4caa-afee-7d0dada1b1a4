Now deploy an MCP server to Cloud Run directly from the source code.

Create and open a new `Dockerfile` for deploying to Cloud Run:

```bash
cloudshell edit Dockerfile
```

Include the following code in the `Dockerfile` to use the `uv` tool for running the `server.py` file:

```dockerfile
# Use the official Python image
FROM python:3.13-slim

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Install the project into /app
COPY . /app
WORKDIR /app

# Allow statements and log messages to immediately appear in the logs
ENV PYTHONUNBUFFERED=1

# Install dependencies
RUN uv sync

EXPOSE $PORT

# Run the FastMCP server
CMD ["uv", "run", "server.py"]
```

Run the `gcloud` command to deploy the application to Cloud Run

```bash
gcloud run deploy zoo-mcp-server \
  --no-allow-unauthenticated \
  --region=europe-west1 \
  --source=. \
  --labels=dev-tutorial=codelab-mcp
```

Use the `--no-allow-unauthenticated` flag to require authentication. This is important for security reasons. If you don't require authentication, anyone can call your MCP server and potentially cause damage to your system. Confirm the creation of a new Artifact Registry repository Since it is your first time deploying to Cloud Run from source code, you will see:

```
Deploying from source requires an Artifact Registry Docker repository to store built containers. A repository named [cloud-run-source-deploy] in region [europe-west1] will be created. Do you want to continue (Y/n)?
```

Type `Y` and press `Enter`, this will create an Artifact Registry repository for your deployment. This is required for storing the MCP server Docker container for the Cloud Run service. After a few minutes, you will see a message like:

```
Service [zoo-mcp-server] revision [zoo-mcp-server-12345-abc] has been deployed and is serving 100 percent of traffic.
```

**Note:** If you try to visit the URL directly, you will see "Error Forbidden" because your MCP server requires authentication.

You have deployed your MCP server. Now you can use it.


### Enable APIs
Before you can start using this codelab, enable the following APIs by running:
```bash
gcloud services enable run.googleapis.com \
  compute.googleapis.com \
  run.googleapis.com \
  cloudbuild.googleapis.com \
  secretmanager.googleapis.com \
  artifactregistry.googleapis.com
```

### GPU Quota
Review the GPU Quota documentation to confirm how to request quota.
If you encounter any "You do not have quota for using GPUs" errors, confirm your quota on g.co/cloudrun/gpu-quota.
**Note:** If you are using a new project, it may take a few minutes between enabling the API and having the quotas appear in the quota page.

### Hugging Face
This codelab uses a model hosted on Hugging Face. To get this model, request for the Hugging Face user access token with "Read" permission. You will reference this later as `YOUR_HF_TOKEN`.
To use the gemma-3-1b-it model, you must agree to the usage terms.


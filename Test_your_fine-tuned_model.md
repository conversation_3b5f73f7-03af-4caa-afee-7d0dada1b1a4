
In this step, you will prompt your model to test the fine tuning using curl.

Get the service URL for your Cloud Run service:
```bash
SERVICE_URL=$(gcloud run services describe serve-gemma-emoji \
  --region $REGION --format 'value(status.url)')
```

Create your prompt for your model.
```bash
USER_PROMPT="Translate to emoji: I ate a banana for breakfast, later I'm thinking of having soup!"
```

Call your service using curl to prompt your model, filtering the results with jq:
```bash
curl -s -X POST ${SERVICE_URL}/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: bearer $(gcloud auth print-identity-token)" \
  -d @- <<EOF | jq ".choices.message.content"
{ "model": "${NEW_MODEL}",
  "messages": [{
  "role": "user",
  "content": [ { "type": "text", "text": "${USER_PROMPT}"}]
  }]
}
EOF
```

You should see a response similar to the following:
```
\ud83c\udf4c\ud83e\udd14\ud83d\ude0b\ud83e\udd63
```

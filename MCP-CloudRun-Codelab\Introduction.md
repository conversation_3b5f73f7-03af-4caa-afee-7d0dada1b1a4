## Overview

In this lab, you will build and deploy a Model Context Protocol (MCP) server. MCP servers are useful for providing LLMs with access to external tools and services. You will configure it as a secure, production-ready service on Cloud Run that can be accessed from multiple clients. Then you will connect to the remote MCP server from Gemini CLI.

### What you'll do

We will use FastMCP to create a zoo MCP server that has two tools: `get_animals_by_species` and `get_animal_details`. FastMCP provides a quick, Pythonic way to build MCP servers and clients.

![Zoo MCP Server Graphic](https://codelabs.developers.google.com/codelabs/cloud-run/img/5a1f3b0b0b0b0b0b.png)

### What you'll learn

- Deploy the MCP server to Cloud Run.
- Secure your server's endpoint by requiring authentication for all requests, ensuring only authorized clients and agents can communicate with it.
- Connect to your secure MCP server endpoint from Gemini CLI
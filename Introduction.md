

In this codelab, you will use Cloud Run jobs to finetune a Gemma 3 model, then serve the result on Cloud Run using vLLM.

### What you'll do
- Train a model to respond to a specific phrase with a specific result using the KomeijiForce/Text2Emoji dataset, established as part of EmojiLM: Modeling the New Emoji Language.
- After training, the model responds to a sentence prefixed with "Translate to emoji: ", with a series of emoji corresponding to that sentence.

### What you'll learn
- How to conduct fine tuning using Cloud Run Jobs GPU
- How to serve a model using Cloud Run with vLLM
- How to use Direct VPC configuration for a GPU Job for faster upload and serving of the model


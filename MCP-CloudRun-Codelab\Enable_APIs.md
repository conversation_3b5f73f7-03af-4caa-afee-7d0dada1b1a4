In the terminal, enable the APIs:

```bash
gcloud services enable \
  run.googleapis.com \
  artifactregistry.googleapis.com \
  cloudbuild.googleapis.com
```

If prompted to authorize, click `Authorize` to continue. <PERSON>lick to authorize Cloud Shell

This command may take a few minutes to complete, but it should eventually produce a successful message similar to this one:

```
Operation "operations/acf.p2-73d90d00-47ee-447a-b600" finished successfully.
```
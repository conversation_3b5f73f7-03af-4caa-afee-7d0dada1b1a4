

In the next step, you'll create the code that does the following:
- Imports the Gemma model from Hugging Face
- Performs fine tuning on the model with the dataset from Hugging Face. The job uses single L4 GPU for fine tuning.
- Uploads the fine-tuned model called new_model to your Cloud Storage bucket

Create a directory for your fine tuning job code.
```bash
mkdir codelab-finetuning-job
cd codelab-finetuning-job
```

Create a file called `finetune.py`
```python
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import os
import torch
from datasets import load_dataset
from peft import LoraConfig, PeftModel
from transformers (
  AutoModelForCausalLM,
  AutoTokenizer,
  BitsAndBytesConfig,
  TrainingArguments,
)
from trl import SFTTrainer

# Cloud Storage bucket to upload the model
bucket_name = os.getenv("BUCKET_NAME", "YOUR_BUCKET_NAME")
# The model that you want to train from the Hugging Face hub
model_name = os.getenv("MODEL_NAME", "google/gemma-3-1b-it")
# The instruction dataset to use
dataset_name = "KomeijiForce/Text2Emoji"
# Fine-tuned model name
new_model = os.getenv("NEW_MODEL", "gemma-emoji")

############################ Setup ############################################
# Load the entire model on the GPU 0
device_map = {"": torch.cuda.current_device()}
# Limit dataset to a random selection
dataset = load_dataset(dataset_name, split="train").shuffle(seed=42).select(range(1000))
# Setup input formats: trains the model to respond to "Translate to emoji:" with emoji output.
tokenizer = AutoTokenizer.from_pretrained(model_name)
def format_to_chat(example):
  return {
  "conversations": [
  {"role": "user", "content": f"Translate to emoji: {example['text']}"},
  {"role": "assistant", "content": example["emoji"]},
  ]
  }
formatted_dataset = dataset.map(
  format_to_chat,
  batched=False, # Process row by row
  remove_columns=dataset.column_names, # Optional: Keep only the new column
)
def apply_chat_template(examples):
  texts = tokenizer.apply_chat_template(examples["conversations"], tokenize=False)
  return {"text": texts}
final_dataset = formatted_dataset.map(apply_chat_template, batched=True)

############################# Config #########################################
# Load tokenizer and model with QLoRA configuration
bnb_4bit_compute_dtype = "float16" # Compute dtype for 4-bit base models
compute_dtype = getattr(torch, bnb_4bit_compute_dtype)
bnb_config = BitsAndBytesConfig(
  load_in_4bit=True, # Activate 4-bit precision base model loading
  bnb_4bit_quant_type="nf4", # Quantization type (fp4 or nf4)
  bnb_4bit_compute_dtype=compute_dtype,
  bnb_4bit_use_double_quant=False, # Activate nested quantization for 4-bit base models (double quantization)
)
# Load base model
model = AutoModelForCausalLM.from_pretrained(
  model_name,
  quantization_config=bnb_config,
  device_map=device_map,
  torch_dtype=torch.float16,
)
model.config.use_cache = False
model.config.pretraining_tp = 1

############################## Train ##########################################
# Load LoRA configuration
peft_config = LoraConfig(
  lora_alpha=16, # Alpha parameter for LoRA scaling
  lora_dropout=0.1, # Dropout probability for LoRA layers,
  r=8, # LoRA attention dimension
  bias="none",
  task_type="CAUSAL_LM",
  target_modules=["q_proj", "v_proj"],
)
# Set training parameters
training_arguments = TrainingArguments(
  output_dir="./results",
  num_train_epochs=1,
  per_device_train_batch_size=1, # Batch size per GPU for training
  gradient_accumulation_steps=2, # Number of update steps to accumulate the gradients for
  optim="paged_adamw_32bit",
  save_steps=0,
  logging_steps=5,
  learning_rate=2e-4, # Initial learning rate (AdamW optimizer)
  weight_decay=0.001, # Weight decay to apply to all layers except bias/LayerNorm weights
  fp16=True, bf16=False, # Enable fp16/bf16 training
  max_grad_norm=0.3, # Maximum gradient normal (gradient clipping)
  warmup_ratio=0.03, # Ratio of steps for a linear warmup (from 0 to learning rate)
  group_by_length=True, # Group sequences into batches with same length # Saves memory and speeds up training considerably
  lr_scheduler_type="cosine",
)
trainer = SFTTrainer(
  model=model,
  train_dataset=final_dataset,
  peft_config=peft_config,
  dataset_text_field="text",
  max_seq_length=512, # Maximum sequence length to use
  tokenizer=tokenizer,
  args=training_arguments,
  packing=False, # Pack multiple short examples in the same input sequence to increase efficiency
)
trainer.train()
trainer.model.save_pretrained(new_model)

################################# Save ########################################
# Reload model in FP16 and merge it with LoRA weights
base_model = AutoModelForCausalLM.from_pretrained(
  model_name,
  low_cpu_mem_usage=True,
  return_dict=True,
  torch_dtype=torch.float16,
  device_map=device_map,
)
model = PeftModel.from_pretrained(base_model, new_model)
model = model.merge_and_unload()
# push results to Cloud Storage
file_path_to_save_the_model = "/finetune/new_model"
model.save_pretrained(file_path_to_save_the_model)
tokenizer.save_pretrained(file_path_to_save_the_model)
```

Create a `requirements.txt` file:
```
accelerate==0.34.2
bitsandbytes==0.45.5
datasets==2.19.1
transformers==4.51.3
peft==0.11.1
trl==0.8.6
torch==2.3.0
```

Create a `Dockerfile`:
```dockerfile
FROM nvidia/cuda:12.6.2-runtime-ubuntu22.04
RUN apt-get update && \
  apt-get -y --no-install-recommends install python3-dev gcc python3-pip git && \
  rm -rf /var/lib/apt/lists/*
COPY requirements.txt /requirements.txt
RUN pip3 install -r requirements.txt --no-cache-dir
COPY finetune.py /finetune.py
ENV PYTHONUNBUFFERED 1
CMD python3 /finetune.py --device cuda
```

Build the container in your Artifact Registry repository:
```bash
gcloud builds submit \
  --tag $REGION-docker.pkg.dev/$PROJECT_ID/$AR_REPO/$IMAGE_NAME \
  --region $REGION
```
